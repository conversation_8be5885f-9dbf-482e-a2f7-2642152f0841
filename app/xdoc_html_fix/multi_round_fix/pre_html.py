# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:24
import re
import json
import asyncio
from typing import List, Dict, Any

from app.basic.util import html_util
from app.enums.prompt import GetPrompt
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.basic.log import logger


class PreHtml:
    """
    使用规则或大模型预处理 html
    """

    # 先散着写，等行程规模了拆分函数
    def __init__(self, html_data: str, subject: str, task_id: str, is_wps: bool):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id
        self.is_wps = is_wps
        self.html_list = html_util.split_html_v2(self.html_data)

    async def main(self):
        for index, html_str in enumerate(self.html_list):
            if '如图所示,电源电压保持不变' in html_str:
                print()

            if '课件待替换标题' in html_str:
                html_str = ''
            pattern = re.compile(r'(<p[^<>]*?>)(\d+\.)(\s*\(\d\d\d\d.*?\))')
            if pattern.search(html_str):
                html_str = pattern.sub(
                    lambda x: f'{x.group(1)}<span data-label="quest_num" data-level="1">{x.group(2)}</span>{x.group(3)}',
                    html_str)
            self.html_list[index] = html_str

        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list)
        self.html_data = self.pre_serial_number()

        # wps 清洗掉标题
        if self.is_wps:
            self.html_data = re.sub(
                r'<p[^>]*?data-label="header" data-level="\d+"[^>]*?>',
                lambda x: '<p>', self.html_data)

        # 把所有的 【答案】加 discard
        self.html_data = self.html_data.replace('【答案】', '<span data-label="discard">【答案】</span>')

        # 处理 html_data，将其转换为特定格式的数组
        simplified_html_list = []
        html_elements = html_util.split_html_v2(self.html_data)

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = f"{index}:{data_label}@{content}"
            simplified_html_list.append(simplified_item)

        # 处理简化后的数据列表
        processed_result = self.process_simplified_list(simplified_html_list)

        # 执行AI判断explanation功能
        if processed_result:
            try:
                ai_judgment_result = await self.ai_judge_explanation(processed_result)
                logger.info(f"AI判断完成，识别出需要重新标记的内容")

                # 应用AI判断结果到HTML数据
                self._apply_ai_judgment_to_html(ai_judgment_result, simplified_html_list, html_elements)

            except Exception as e:
                logger.error(f"AI判断失败: {e}")
                # AI判断失败不影响主流程，继续执行

        return self.html_data

    def pre_serial_number(self):
        block_list = self.get_block_list()
        for item in block_list:
            sub_html_list, min_line, max_line = item['sub_html_list'], item['min_line'], item['max_line']
            # 把一级题号标签去掉、如果没有解析标签，加上
            for _i, _s in enumerate(sub_html_list):
                _s = re.sub(
                    r'<span[^>]*?data-label="quest_num" data-level="\d"[^>]*?>(.*?)</span>',
                    lambda x: x.group(1), _s)
                if 'data-label="explanation"' not in _s:
                    _s = _s.replace('<p', '<p data-label="explanation"')
                sub_html_list[_i] = _s

            sub_html_data = html_util.join_html(sub_html_list, is_del_line=False)
            # 把所有的数据写到开始的 line，其余 line 置空
            for i in range(min_line, max_line + 1):
                if i == min_line:
                    self.html_list[i] = sub_html_data
                else:
                    self.html_list[i] = ''
        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list, is_del_line=False)

        # 如果是 wps 且没有一级题号，则补充假题号
        if self.is_wps and not re.search(r'<span[^>]*?data-label="quest_num" data-level="1"[^>]*?>', self.html_data):
            self.html_data = re.sub(
                r'<p[^>]*?>', lambda x: x.group() + '<span data-label="quest_num" data-level="-1">1.</span>',
                self.html_data, count=1)
        return self.html_data

    def get_block_list(self):
        """
        切割 block
        """
        def _helper():
            if analysis_list:
                min_line = min(line_list)
                max_line = max(line_list)
                group_list.append({
                    'sub_html_list': analysis_list,
                    'min_line': min_line,
                    'max_line': max_line,
                })

        group_list = []
        analysis_list = []
        line_list = []
        next_index = -1
        for index, s in enumerate(self.html_list):
            if '从图上看，起飞后第25秒时' in s:
                print()

            if next_index > index:
                continue

            if index == len(self.html_list) - 1:
                analysis_list.append(s)
                line_list.append(index)
                _helper()
                analysis_list, line_list = [], []
            elif 'data-label="explanation"' in s:
                analysis_list.append(s)
                line_list.append(index)
            elif 'data-label="header"' in s:
                _helper()
                analysis_list, line_list = [], []
            else:
                if not analysis_list:
                    continue
                # 判断 block 的结束在哪里
                # 如果 s 的下一个还是解析或者是题号，则 s 加入到当前 block
                next_s = self.html_list[index + 1]
                if 'data-label="explanation"' in next_s or ('data-label="quest_num"' in next_s and 'data-level="1"' in next_s):
                    analysis_list.append(s)
                    line_list.append(index)
                else:
                    # 往下探查 4 行，如果遇到一级题号、标题，则把其前面都加入到当前 block；如果没有遇到，则 block 截止到当前行
                    is_merge_down = False
                    # 从当前行的下一行开始下探，把当前行先加到 temp list
                    temp_list = [self.html_list[index]]
                    temp_line_list = [index]
                    for _i in range(index + 1, index + 5):
                        tmp = self.html_list[_i]
                        if ('data-label="quest_num"' in tmp and 'data-level="1"' in tmp) or 'data-label="header"' in tmp:
                            is_merge_down = True
                            next_index = _i
                            break
                        elif _i == len(self.html_list) - 1:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                            is_merge_down = True
                            next_index = _i
                            break
                        else:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                    if is_merge_down:
                        analysis_list += temp_list
                        line_list += temp_line_list

                    _helper()
                    analysis_list, line_list = [], []

        return group_list

    def process_simplified_list(self, simplified_html_list):
        """
        处理简化后的HTML列表，按照要求进行分组和拆分

        修改后的逻辑：
        1. 不按照 header 分组，直接在整个列表中找所有连续的 explanation
        2. 找到连续的 explanation 后，全部保留（不只取后面三行）
        3. 每组连续的 explanation + 后面的所有非 explanation 内容作为一个 processed_result 的项
        4. 分割策略：按照固定10个元素进行分割（从8个增加到10个）

        Args:
            simplified_html_list: 格式为 "index:data-label@content" 的列表

        Returns:
            list: 处理后的结果，格式为 [{index: 0, list: [[...], [...]]}, ...]
                  每个连续explanation组作为一个独立的结果项
        """
        # 1. 直接在整个列表中找到所有连续的explanation组
        consecutive_explanation_groups = self._find_consecutive_explanation_groups(simplified_html_list)

        # 2. 处理每个连续explanation组
        result = []
        result_index = 0

        for explanation_group in consecutive_explanation_groups:
            if explanation_group:
                # 全部保留explanation（不过滤）
                all_explanations = explanation_group

                # 收集最后一个explanation后面的所有非explanation内容，直到遇到下一个explanation
                last_explanation_index = all_explanations[-1]['index']
                unlabeled_content = self._collect_content_until_next_explanation(simplified_html_list, last_explanation_index)

                # 构建这个连续explanation组的结果
                explanation_list = [item['item'] for item in all_explanations]
                all_content = explanation_list + unlabeled_content  # 合并explanation和其他内容

                # 按照固定10个元素进行分割
                result_lists = []
                for i in range(0, len(all_content), 10):
                    chunk = all_content[i:i+10]
                    result_lists.append(chunk)

                result.append({
                    'index': result_index,
                    'list': result_lists
                })
                result_index += 1

        return result

    def _group_by_header(self, simplified_html_list):
        """
        按照 header 标签进行分组

        Args:
            simplified_html_list: 简化的HTML列表

        Returns:
            list: 分组后的二维数组
        """
        groups = []
        current_group = []

        for item in simplified_html_list:
            # 解析格式：index:data-label@content
            parts = item.split(':', 1)
            if len(parts) < 2:
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                continue

            data_label = label_parts[0]

            # 如果遇到 header，开始新的分组
            if data_label == 'header':
                if current_group:
                    groups.append(current_group)
                current_group = [item]
            else:
                current_group.append(item)

        # 添加最后一个分组
        if current_group:
            groups.append(current_group)

        return groups



    def _find_consecutive_explanation_groups(self, group):
        """
        找到所有连续的 explanation 组

        Args:
            group: 一个分组的数据

        Returns:
            list: 连续explanation组的列表，每个组包含连续的explanation项目及其在group中的索引
        """
        consecutive_groups = []
        current_group = []

        for i, item in enumerate(group):
            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []
                continue

            data_label = label_parts[0]

            # 如果是 explanation，加入当前组
            if data_label == 'explanation':
                current_group.append({
                    'item': item,
                    'index': i
                })
            else:
                # 如果不是 explanation，结束当前组
                if current_group:
                    consecutive_groups.append(current_group)
                    current_group = []

        # 添加最后一个组
        if current_group:
            consecutive_groups.append(current_group)

        return consecutive_groups



    def _collect_unlabeled_content_after_index(self, group, start_index):
        """
        从指定索引后开始收集无 label 内容，直到遇到有 label 的内容

        Args:
            group: 分组数据
            start_index: 开始收集的索引位置

        Returns:
            list: 收集到的无label内容列表
        """
        unlabeled_items = []
        j = start_index + 1

        while j < len(group):
            next_item = group[j]
            next_parts = next_item.split(':', 1)
            if len(next_parts) < 2:
                j += 1
                continue

            next_label_content = next_parts[1]
            next_label_parts = next_label_content.split('@', 1)
            if len(next_label_parts) < 2:
                j += 1
                continue

            next_data_label = next_label_parts[0]

            # 如果遇到有 label 的内容（非空label），立即停止收集
            if next_data_label and next_data_label.strip():
                break

            # 如果是无 label 的内容，继续收集
            unlabeled_items.append(next_item)
            j += 1

        return unlabeled_items

    def _collect_content_until_next_explanation(self, simplified_html_list, start_index):
        """
        从指定索引后开始收集所有非explanation内容，直到遇到下一个explanation

        Args:
            simplified_html_list: 完整的简化HTML列表
            start_index: 开始收集的索引位置

        Returns:
            list: 收集到的所有非explanation内容列表
        """
        collected_items = []
        j = start_index + 1

        while j < len(simplified_html_list):
            next_item = simplified_html_list[j]
            next_parts = next_item.split(':', 1)
            if len(next_parts) < 2:
                collected_items.append(next_item)
                j += 1
                continue

            next_label_content = next_parts[1]
            next_label_parts = next_label_content.split('@', 1)
            if len(next_label_parts) < 2:
                collected_items.append(next_item)
                j += 1
                continue

            next_data_label = next_label_parts[0]

            # 如果遇到explanation，立即停止收集
            if next_data_label == 'explanation':
                break

            # 收集所有非explanation内容（包括有其他标签的内容）
            collected_items.append(next_item)
            j += 1

        return collected_items

    async def ai_judge_explanation(self, processed_result: List[Dict[str, Any]], model: str = DBModel.V15_PRO_32.value) -> Dict[str, Any]:
        """
        使用AI判断哪些内容应该被标记为explanation

        Args:
            processed_result: process_simplified_list的处理结果
            model: 使用的AI模型

        Returns:
            Dict: 包含原始数据和AI判断结果的字典
        """
        logger.info(f"开始AI判断explanation，共{len(processed_result)}个分组")

        ai_results = []

        for group_idx, group in enumerate(processed_result):
            logger.info(f"处理分组 {group_idx}，共{len(group['list'])}个explanation块")

            group_results = []
            explanation_blocks = self._group_explanation_blocks(group['list'])

            for block_idx, explanation_block in enumerate(explanation_blocks):
                logger.info(f"处理explanation块 {block_idx}，共{len(explanation_block)}个子块")

                block_result = await self._process_explanation_block(explanation_block, model)
                if block_result:
                    group_results.append({
                        'block_index': block_idx,
                        'results': block_result
                    })

            if group_results:
                ai_results.append({
                    'group_index': group_idx,
                    'blocks': group_results
                })

        return {
            'original_processed_result': processed_result,
            'ai_judgment_results': ai_results
        }

    def _group_explanation_blocks(self, explanation_list: List[List[str]]) -> List[List[List[str]]]:
        """
        将explanation块按照原始explanation进行分组

        Args:
            explanation_list: 所有的explanation块列表

        Returns:
            List: 按原始explanation分组的块列表
        """
        grouped_blocks = []
        current_group = []
        current_explanation = None

        for block in explanation_list:
            # 找到当前块中的explanation
            block_explanation = None
            for item in block:
                parts = item.split(':', 1)
                if len(parts) >= 2:
                    label_content = parts[1]
                    label_parts = label_content.split('@', 1)
                    if len(label_parts) >= 2 and label_parts[0] == 'explanation':
                        block_explanation = item
                        break

            if block_explanation:
                # 如果是新的explanation，开始新的分组
                if current_explanation != block_explanation:
                    if current_group:
                        grouped_blocks.append(current_group)
                    current_group = [block]
                    current_explanation = block_explanation
                else:
                    # 同一个explanation的后续块
                    current_group.append(block)

        # 添加最后一个分组
        if current_group:
            grouped_blocks.append(current_group)

        return grouped_blocks

    async def _process_explanation_block(self, explanation_block: List[List[str]], model: str) -> List[Dict[str, Any]]:
        """
        处理单个explanation块的所有子块

        Args:
            explanation_block: 同一个explanation的所有子块
            model: AI模型

        Returns:
            List: AI判断结果列表
        """
        results = []

        for sub_block_idx, sub_block in enumerate(explanation_block):
            logger.info(f"处理子块 {sub_block_idx}")

            # 准备AI判断的输入数据
            input_data = self._prepare_ai_input(sub_block)

            try:
                # 调用AI进行判断
                ai_result = await self._call_ai_judgment(input_data, model)

                if ai_result:
                    results.append({
                        'sub_block_index': sub_block_idx,
                        'input_data': input_data,
                        'ai_result': ai_result,
                        'original_block': sub_block
                    })

                # 检查连续性：如果当前子块的最后一个项目不是explanation，停止处理后续子块
                if not self._should_continue_processing(ai_result, sub_block):
                    logger.info(f"子块 {sub_block_idx} 的最后项目不是explanation，停止处理后续子块")
                    break

            except Exception as e:
                logger.error(f"AI判断失败: {e}")
                # 发生错误时也停止处理后续子块
                break

        return results

    def _prepare_ai_input(self, sub_block: List[str]) -> str:
        """
        准备AI判断的输入数据

        Args:
            sub_block: 子块数据

        Returns:
            str: 格式化的输入数据
        """
        input_lines = []

        for item in sub_block:
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    content = label_parts[1]

                    if data_label == 'explanation':
                        input_lines.append(f"{index}: explanation@{content}")
                    else:
                        input_lines.append(f"{index}: @{content}")

        return '\n'.join(input_lines)

    async def _call_ai_judgment(self, input_data: str, model: str) -> List[str]:
        """
        调用AI进行判断

        Args:
            input_data: 输入数据
            model: AI模型

        Returns:
            List[str]: AI判断结果的ID列表
        """
        try:
            # 获取prompt
            prompt = GetPrompt.analysis_fix(input_data)

            # 调用AI
            response, tokens = await Doubao.async_chat(prompt, model, temperature=0.1)

            logger.info(f"AI调用成功，使用tokens: {tokens}")

            # 解析JSON响应
            try:
                # 清理响应中的markdown标记
                cleaned_response = self._clean_ai_response(response)
                result = json.loads(cleaned_response)
                if isinstance(result, list):
                    return result
                else:
                    logger.warning(f"AI返回格式不正确: {response}")
                    return []
            except json.JSONDecodeError as e:
                logger.error(f"AI返回JSON解析失败: {e}, response: {response}")
                return []

        except Exception as e:
            logger.error(f"AI调用失败: {e}")
            raise

    def _clean_ai_response(self, response: str) -> str:
        """
        清理AI响应中的markdown标记和其他格式化字符

        Args:
            response: 原始AI响应

        Returns:
            str: 清理后的JSON字符串
        """
        # 去除首尾空白
        cleaned = response.strip()

        # 移除可能的markdown代码块标记
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:]  # 移除 '```json'
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:]   # 移除 '```'

        if cleaned.endswith('```'):
            cleaned = cleaned[:-3]  # 移除结尾的 '```'

        # 再次去除空白
        cleaned = cleaned.strip()

        # 移除可能的其他格式化标记
        lines = cleaned.split('\n')
        json_lines = []
        in_json = False

        for line in lines:
            line = line.strip()
            # 跳过空行和注释行
            if not line or line.startswith('#') or line.startswith('//'):
                continue

            # 检查是否是JSON开始
            if line.startswith('[') or line.startswith('{'):
                in_json = True

            if in_json:
                json_lines.append(line)

            # 检查是否是JSON结束
            if line.endswith(']') or line.endswith('}'):
                break

        # 如果找到了JSON内容，返回拼接的结果
        if json_lines:
            result = '\n'.join(json_lines)
            logger.debug(f"清理后的AI响应: {result}")
            return result

        # 如果没有找到明确的JSON结构，返回原始清理结果
        logger.debug(f"未找到明确JSON结构，返回清理后的响应: {cleaned}")
        return cleaned

    def _should_continue_processing(self, ai_result: List[str], sub_block: List[str]) -> bool:
        """
        判断是否应该继续处理下一个子块

        Args:
            ai_result: AI判断结果
            sub_block: 当前子块

        Returns:
            bool: 是否继续处理
        """
        if not ai_result:
            return False

        # 获取子块中最后一个非explanation项目的ID
        last_non_explanation_id = None
        for item in reversed(sub_block):
            parts = item.split(':', 1)
            if len(parts) >= 2:
                index = parts[0]
                label_content = parts[1]
                label_parts = label_content.split('@', 1)
                if len(label_parts) >= 2:
                    data_label = label_parts[0]
                    if data_label != 'explanation':
                        last_non_explanation_id = index
                        break

        # 如果最后一个非explanation项目在AI结果中，说明应该继续处理
        return last_non_explanation_id in ai_result if last_non_explanation_id else False

    def _apply_ai_judgment_to_html(self, ai_judgment_result: Dict[str, Any], simplified_html_list: List[str], html_elements: List[str]):
        """
        将AI判断结果应用到HTML数据中

        Args:
            ai_judgment_result: AI判断结果
            simplified_html_list: 简化的HTML列表
            html_elements: 原始HTML元素列表
        """
        try:
            # 1. 提取所有需要标记为explanation的ID
            ids_to_mark = self._extract_ids_from_ai_result(ai_judgment_result)

            if not ids_to_mark:
                logger.info("AI判断结果中没有需要重新标记的内容")
                return

            logger.info(f"AI识别出 {len(ids_to_mark)} 个项目需要标记为explanation: {ids_to_mark}")

            # 2. 将ID映射到HTML元素索引
            id_to_element_index = self._map_ids_to_element_indices(ids_to_mark, simplified_html_list)

            # 3. 更新HTML元素
            updated_count = self._update_html_elements(id_to_element_index, html_elements)

            # 4. 重新构建HTML数据
            if updated_count > 0:
                self.html_data = html_util.join_html(html_elements)
                logger.info(f"成功更新了 {updated_count} 个HTML元素的data-label属性")
            else:
                logger.info("没有HTML元素需要更新")

        except Exception as e:
            logger.error(f"应用AI判断结果失败: {e}")
            # 不抛出异常，避免影响主流程

    def _extract_ids_from_ai_result(self, ai_judgment_result: Dict[str, Any]) -> List[str]:
        """
        从AI判断结果中提取所有需要标记的ID

        Args:
            ai_judgment_result: AI判断结果

        Returns:
            List[str]: 需要标记为explanation的ID列表
        """
        ids_to_mark = []

        ai_results = ai_judgment_result.get('ai_judgment_results', [])
        for group_result in ai_results:
            for block_result in group_result.get('blocks', []):
                for sub_result in block_result.get('results', []):
                    ai_result = sub_result.get('ai_result', [])
                    if isinstance(ai_result, list):
                        ids_to_mark.extend(ai_result)

        # 去重并保持顺序
        unique_ids = []
        seen = set()
        for id_str in ids_to_mark:
            if id_str not in seen:
                unique_ids.append(id_str)
                seen.add(id_str)

        return unique_ids

    def _map_ids_to_element_indices(self, ids_to_mark: List[str], simplified_html_list: List[str]) -> Dict[str, int]:
        """
        将ID映射到HTML元素索引

        Args:
            ids_to_mark: 需要标记的ID列表
            simplified_html_list: 简化的HTML列表

        Returns:
            Dict[str, int]: ID到元素索引的映射
        """
        id_to_element_index = {}

        for simplified_item in simplified_html_list:
            # 解析格式：index:data-label@content
            parts = simplified_item.split(':', 1)
            if len(parts) >= 2:
                item_index = parts[0]
                if item_index in ids_to_mark:
                    try:
                        element_index = int(item_index)
                        id_to_element_index[item_index] = element_index
                    except ValueError:
                        logger.warning(f"无法解析索引: {item_index}")

        return id_to_element_index

    def _update_html_elements(self, id_to_element_index: Dict[str, int], html_elements: List[str]) -> int:
        """
        更新HTML元素的data-label属性

        Args:
            id_to_element_index: ID到元素索引的映射
            html_elements: HTML元素列表

        Returns:
            int: 更新的元素数量
        """
        updated_count = 0

        for item_id, element_index in id_to_element_index.items():
            if 0 <= element_index < len(html_elements):
                original_element = html_elements[element_index]
                updated_element = self._add_or_update_explanation_label(original_element)

                if updated_element != original_element:
                    html_elements[element_index] = updated_element
                    updated_count += 1
                    logger.debug(f"更新元素 {element_index} (ID: {item_id})")
            else:
                logger.warning(f"元素索引 {element_index} 超出范围")

        return updated_count

    def _add_or_update_explanation_label(self, html_element: str) -> str:
        """
        为HTML元素添加或更新data-label="explanation"属性

        Args:
            html_element: 原始HTML元素字符串

        Returns:
            str: 更新后的HTML元素字符串
        """
        # 检查是否是p标签
        if not html_element.strip().startswith('<p'):
            return html_element

        # 使用正则表达式处理p标签
        p_tag_pattern = re.compile(r'<p([^>]*)>')
        match = p_tag_pattern.search(html_element)

        if not match:
            return html_element

        attributes = match.group(1)

        # 检查是否已有data-label属性
        data_label_pattern = re.compile(r'\s*data-label="[^"]*"')

        if data_label_pattern.search(attributes):
            # 替换现有的data-label属性
            new_attributes = data_label_pattern.sub(' data-label="explanation"', attributes)
        else:
            # 添加新的data-label属性
            new_attributes = attributes + ' data-label="explanation"'

        # 清理多余的空格
        new_attributes = re.sub(r'\s+', ' ', new_attributes).strip()

        # 构建新的p标签
        if new_attributes:
            new_p_tag = f'<p {new_attributes}>'
        else:
            new_p_tag = '<p data-label="explanation">'

        # 替换原始HTML中的p标签
        updated_element = p_tag_pattern.sub(new_p_tag, html_element)

        return updated_element


if __name__ == '__main__':
    async def test_main():
        url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'
        import requests
        html_data = requests.get(url).content.decode('utf-8')
        result = await PreHtml(html_data, 'biology', '111111', False).main()
        print(f"Result type: {type(result)}")

    asyncio.run(test_main())
